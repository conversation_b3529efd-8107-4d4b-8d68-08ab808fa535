import { useState, useEffect, useMemo } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/hooks/use-toast';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import {
  Search,
  ClipboardList,
  CheckCircle,
  AlertCircle,
  Clock,
  ArrowRight,
  PieChart,
  BarChart,
  RefreshCw,
  ChevronDown,
  ChevronRight,
  Users,
  Globe,
  PoundSterling,
  Filter,
  Plus,
  MessageSquare,
  DollarSign,
  XCircle
} from 'lucide-react';
import { Loader2 } from 'lucide-react';

// Define types for our data
type Task = {
  id: string;
  title: string;
  description: string;
  status: 'open' | 'assigned' | 'in_progress' | 'pending_payment' | 'completed' | 'confirmed' | 'cancelled';
  created_by?: string;
  user_id: string;
  assigned_to?: string;
  created_at: string;
  due_date?: string;
  organization_id?: string;
  category?: string;
  location?: string;
  budget?: number;
  visibility: 'admin' | 'internal' | 'public'; // Determines if task is internal or external
  payment_status?: 'unpaid' | 'pending' | 'processing' | 'paid' | 'not_required';
  offers_count?: number;
};

const OrganizationTaskManagement = () => {
  const { organizationId, user } = useAuth();
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState<'all' | 'internal' | 'external' | 'pending' | 'in-progress' | 'completed' | 'cancelled'>('all');
  const [statusFilter, setStatusFilter] = useState<'all' | 'open' | 'assigned' | 'in_progress' | 'completed'>('all');
  const [assignmentFilter, setAssignmentFilter] = useState<'all' | 'internal' | 'external'>('all');
  const [statsVisible, setStatsVisible] = useState(true);

  // Enhanced task statistics
  const [taskStats, setTaskStats] = useState({
    total: 0,
    open: 0,
    assigned: 0,
    inProgress: 0,
    completed: 0,
    cancelled: 0,
    internal: 0,
    external: 0,
    totalBudget: 0,
    pendingBudget: 0,
    paidBudget: 0
  });

  useEffect(() => {
    if (organizationId) {
      fetchTasks();
    }
  }, [organizationId]);

  const fetchTasks = async () => {
    try {
      setLoading(true);

      // Fetch tasks for the organization
      // We need to fetch tasks created by users in this organization
      const { data: profilesData, error: profilesError } = await supabase
        .from('profiles')
        .select('id')
        .eq('organization_id', organizationId);

      if (profilesError) {
        console.error('Error fetching organization profiles:', profilesError);
        return;
      }

      const orgUserIds = profilesData.map(profile => profile.id);

      // Fetch all tasks created by organization members
      const { data, error } = await supabase
        .from('tasks')
        .select('*')
        .in('user_id', orgUserIds)
        .order('created_at', { ascending: false });

      if (error) throw error;

      const tasksData = data || [];
      setTasks(tasksData);

      // Calculate task statistics
      const stats = {
        total: tasksData.length,
        open: tasksData.filter(t => t.status === 'open').length,
        assigned: tasksData.filter(t => t.status === 'assigned').length,
        inProgress: tasksData.filter(t => t.status === 'in_progress').length,
        completed: tasksData.filter(t => t.status === 'closed' || t.status === 'confirmed').length,
        cancelled: tasksData.filter(t => t.status === 'cancelled').length,
        internal: tasksData.filter(t => t.visibility === 'internal' || t.visibility === 'admin').length,
        external: tasksData.filter(t => t.visibility === 'public').length,
        totalBudget: tasksData.reduce((sum, task) => sum + (task.budget || 0), 0),
        pendingBudget: tasksData.filter(t =>
          t.visibility === 'public' &&
          t.status !== 'closed' &&
          t.status !== 'confirmed' &&
          t.status !== 'cancelled'
        ).reduce((sum, task) => sum + (task.budget || 0), 0),
        paidBudget: tasksData.filter(t =>
          t.visibility === 'public' &&
          (t.status === 'closed' || t.status === 'confirmed') &&
          t.payment_status === 'paid'
        ).reduce((sum, task) => sum + (task.budget || 0), 0)
      };

      setTaskStats(stats);
    } catch (error: any) {
      console.error('Error fetching tasks:', error.message);
      toast({
        title: 'Error fetching tasks',
        description: error.message,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-200 flex items-center gap-1">
            <Clock className="h-3 w-3" />
            Pending
          </Badge>
        );
      case 'assigned':
        return (
          <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200 flex items-center gap-1">
            <ClipboardList className="h-3 w-3" />
            Assigned
          </Badge>
        );
      case 'in_progress':
        return (
          <Badge variant="outline" className="bg-purple-100 text-purple-800 border-purple-200 flex items-center gap-1">
            <Clock className="h-3 w-3" />
            In Progress
          </Badge>
        );
      case 'completed':
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200 flex items-center gap-1">
            <CheckCircle className="h-3 w-3" />
            Completed
          </Badge>
        );
      case 'confirmed':
        return (
          <Badge variant="outline" className="bg-teal-100 text-teal-800 border-teal-200 flex items-center gap-1">
            <CheckCircle className="h-3 w-3" />
            Confirmed
          </Badge>
        );
      case 'closed':
        return (
          <Badge variant="outline" className="bg-gray-100 text-gray-800 border-gray-200 flex items-center gap-1">
            <CheckCircle className="h-3 w-3" />
            Closed
          </Badge>
        );
      case 'open':
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200 flex items-center gap-1">
            <Clock className="h-3 w-3" />
            Open
          </Badge>
        );
      case 'interest':
        return (
          <Badge variant="outline" className="bg-cyan-100 text-cyan-800 border-cyan-200 flex items-center gap-1">
            <MessageSquare className="h-3 w-3" />
            Interest
          </Badge>
        );
      case 'questions':
        return (
          <Badge variant="outline" className="bg-cyan-100 text-cyan-800 border-cyan-200 flex items-center gap-1">
            <MessageSquare className="h-3 w-3" />
            Questions
          </Badge>
        );
      case 'offer':
        return (
          <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200 flex items-center gap-1">
            <DollarSign className="h-3 w-3" />
            Offers
          </Badge>
        );
      case 'pending_payment':
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-200 flex items-center gap-1">
            <DollarSign className="h-3 w-3" />
            Payment Due
          </Badge>
        );
      case 'cancelled':
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800 border-red-200 flex items-center gap-1">
            <XCircle className="h-3 w-3" />
            Cancelled
          </Badge>
        );
      default:
        // Log unknown status for debugging (development only)
        if (process.env.NODE_ENV === 'development') {
          if (process.env.NODE_ENV === 'development') {
    console.log('Unknown task status: completed');
  }
        }
        return (
          <Badge variant="outline" className="bg-gray-100 text-gray-800 border-gray-200 flex items-center gap-1">
            <AlertCircle className="h-3 w-3" />
            Unknown
          </Badge>
        );
    }
  };

  // Get recent tasks (last 5)
  const recentTasks = [...tasks].sort((a, b) =>
    new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
  ).slice(0, 5);

  // Get upcoming tasks (next 5 due)
  const upcomingTasks = [...tasks]
    .filter(task => task.due_date && task.status !== 'closed' && task.status !== 'confirmed')
    .sort((a, b) =>
      new Date(a.due_date || '').getTime() - new Date(b.due_date || '').getTime()
    ).slice(0, 5);

  // Filter tasks based on active tab and search term
  const filteredTasks = useMemo(() => {
    return tasks.filter(task => {
      // Apply search filter
      if (searchTerm && !task.title.toLowerCase().includes(searchTerm.toLowerCase()) &&
          !task.description?.toLowerCase().includes(searchTerm.toLowerCase())) {
        return false;
      }

      // Apply tab filter
      if (activeTab === 'internal' && task.visibility !== 'internal' && task.visibility !== 'admin') {
        return false;
      }
      if (activeTab === 'external' && task.visibility !== 'public') {
        return false;
      }
      if (activeTab === 'pending' && task.status !== 'open' && task.status !== 'assigned') {
        return false;
      }
      if (activeTab === 'in-progress' && task.status !== 'in_progress') {
        return false;
      }
      if (activeTab === 'closed' && task.status !== 'closed' && task.status !== 'confirmed') {
        return false;
      }
      if (activeTab === 'cancelled' && task.status !== 'cancelled') {
        return false;
      }

      return true;
    });
  }, [tasks, searchTerm, activeTab]);

  // Toggle stats visibility
  const toggleStats = () => {
    setStatsVisible(!statsVisible);
  };

  // Get assignment type badge
  const getAssignmentBadge = (visibility: string) => {
    switch (visibility) {
      case 'internal':
      case 'admin':
        return (
          <Badge variant="outline" className="bg-purple-100 text-purple-800 border-purple-200 flex items-center gap-1">
            <Users className="h-3 w-3" />
            Internal
          </Badge>
        );
      case 'public':
        return (
          <Badge variant="outline" className="bg-orange-100 text-orange-800 border-orange-200 flex items-center gap-1">
            <Globe className="h-3 w-3" />
            External
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="bg-gray-100 text-gray-800 border-gray-200 flex items-center gap-1">
            <AlertCircle className="h-3 w-3" />
            Unknown
          </Badge>
        );
    }
  };

  return (
    <div className="space-y-6">
      {loading ? (
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
        </div>
      ) : (
        <>
          {/* Header with controls */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
              <div className="relative w-full sm:w-64">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                  placeholder="Search tasks..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>

            <div className="flex space-x-2">
              <Button variant="outline" onClick={toggleStats}>
                {statsVisible ? <ChevronDown className="h-4 w-4 mr-2" /> : <ChevronRight className="h-4 w-4 mr-2" />}
                {statsVisible ? 'Hide Stats' : 'Show Stats'}
              </Button>
              <Button variant="outline" asChild>
                <Link to="/admin/tasks">
                  <ClipboardList className="h-4 w-4 mr-2" />
                  Task Review
                </Link>
              </Button>
              <Button onClick={fetchTasks}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              <Button asChild>
                <Link to="/post-task">
                  <Plus className="h-4 w-4 mr-2" />
                  New Task
                </Link>
              </Button>
            </div>
          </div>

          {/* Task Statistics Dashboard */}
          {statsVisible && (
            <Card className="border-gray-200">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center">
                  <BarChart className="h-5 w-5 mr-2 text-blue-600" />
                  Tasks Overview
                </CardTitle>
                <CardDescription>
                  Summary of all tasks by status and assignment type
                </CardDescription>
              </CardHeader>
              <CardContent>
                {/* High-level metrics */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <ClipboardList className="h-5 w-5 mr-2 text-blue-500" />
                        <span className="text-sm font-medium">Total Tasks</span>
                      </div>
                      <span className="text-xl font-bold">{taskStats.total}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <XCircle className="h-5 w-5 mr-2 text-red-500" />
                        <span className="text-sm font-medium">Cancelled Tasks</span>
                      </div>
                      <span className="text-xl font-bold">{taskStats.cancelled}</span>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Users className="h-5 w-5 mr-2 text-purple-500" />
                        <span className="text-sm font-medium">Internal Tasks</span>
                      </div>
                      <span className="text-xl font-bold">{taskStats.internal}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Globe className="h-5 w-5 mr-2 text-orange-500" />
                        <span className="text-sm font-medium">External Tasks</span>
                      </div>
                      <span className="text-xl font-bold">{taskStats.external}</span>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <PoundSterling className="h-5 w-5 mr-2 text-green-600" />
                        <span className="text-sm font-medium">Total Budget</span>
                      </div>
                      <span className="text-xl font-bold">£{taskStats.totalBudget.toFixed(2)}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Clock className="h-5 w-5 mr-2 text-amber-500" />
                        <span className="text-sm font-medium">Pending Budget</span>
                      </div>
                      <span className="text-xl font-bold">£{taskStats.pendingBudget.toFixed(2)}</span>
                    </div>
                  </div>
                </div>

                {/* Detailed table view */}
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="bg-gray-50 border-b border-gray-200">
                        <th className="px-4 py-2 text-left font-medium text-gray-600">Assignment Type</th>
                        <th className="px-4 py-2 text-center font-medium text-gray-600">Total</th>
                        <th className="px-4 py-2 text-center font-medium text-red-600">Open</th>
                        <th className="px-4 py-2 text-center font-medium text-blue-600">Assigned</th>
                        <th className="px-4 py-2 text-center font-medium text-yellow-600">In Progress</th>
                        <th className="px-4 py-2 text-center font-medium text-green-600">Closed</th>
                        <th className="px-4 py-2 text-center font-medium text-red-600">Cancelled</th>
                        <th className="px-4 py-2 text-center font-medium text-orange-600">Outstanding</th>
                      </tr>
                    </thead>
                    <tbody>
                      {/* Internal Tasks */}
                      <tr className="border-b border-gray-200 hover:bg-gray-50">
                        <td className="px-4 py-3 font-medium">
                          <div className="flex items-center">
                            <Users className="h-4 w-4 mr-2 text-purple-500" />
                            Internal
                          </div>
                        </td>
                        <td className="px-4 py-3 text-center">
                          <span className="font-semibold text-gray-700">{taskStats.internal}</span>
                        </td>
                        <td className="px-4 py-3 text-center">
                          {tasks.filter(t => (t.visibility === 'internal' || t.visibility === 'admin') && t.status === 'open').length > 0 ? (
                            <span className="font-semibold text-red-600">
                              {tasks.filter(t => (t.visibility === 'internal' || t.visibility === 'admin') && t.status === 'open').length}
                            </span>
                          ) : (
                            <span className="text-gray-500">0</span>
                          )}
                        </td>
                        <td className="px-4 py-3 text-center">
                          {tasks.filter(t => (t.visibility === 'internal' || t.visibility === 'admin') && t.status === 'assigned').length > 0 ? (
                            <span className="font-semibold text-blue-600">
                              {tasks.filter(t => (t.visibility === 'internal' || t.visibility === 'admin') && t.status === 'assigned').length}
                            </span>
                          ) : (
                            <span className="text-gray-500">0</span>
                          )}
                        </td>
                        <td className="px-4 py-3 text-center">
                          {tasks.filter(t => (t.visibility === 'internal' || t.visibility === 'admin') && t.status === 'in_progress').length > 0 ? (
                            <span className="font-semibold text-yellow-600">
                              {tasks.filter(t => (t.visibility === 'internal' || t.visibility === 'admin') && t.status === 'in_progress').length}
                            </span>
                          ) : (
                            <span className="text-gray-500">0</span>
                          )}
                        </td>
                        <td className="px-4 py-3 text-center">
                          {tasks.filter(t => (t.visibility === 'internal' || t.visibility === 'admin') && (t.status === 'closed' || t.status === 'confirmed')).length > 0 ? (
                            <span className="font-semibold text-green-600">
                              {tasks.filter(t => (t.visibility === 'internal' || t.visibility === 'admin') && (t.status === 'closed' || t.status === 'confirmed')).length}
                            </span>
                          ) : (
                            <span className="text-gray-500">0</span>
                          )}
                        </td>
                        <td className="px-4 py-3 text-center">
                          {tasks.filter(t => (t.visibility === 'internal' || t.visibility === 'admin') && t.status === 'cancelled').length > 0 ? (
                            <span className="font-semibold text-red-600">
                              {tasks.filter(t => (t.visibility === 'internal' || t.visibility === 'admin') && t.status === 'cancelled').length}
                            </span>
                          ) : (
                            <span className="text-gray-500">0</span>
                          )}
                        </td>
                        <td className="px-4 py-3 text-center">
                          {tasks.filter(t => (t.visibility === 'internal' || t.visibility === 'admin') && t.status !== 'closed' && t.status !== 'confirmed' && t.status !== 'cancelled').length > 0 ? (
                            <span className="font-semibold text-orange-600">
                              {tasks.filter(t => (t.visibility === 'internal' || t.visibility === 'admin') && t.status !== 'closed' && t.status !== 'confirmed' && t.status !== 'cancelled').length}
                            </span>
                          ) : (
                            <span className="text-gray-500">0</span>
                          )}
                        </td>
                      </tr>

                      {/* External Tasks */}
                      <tr className="border-b border-gray-200 hover:bg-gray-50">
                        <td className="px-4 py-3 font-medium">
                          <div className="flex items-center">
                            <Globe className="h-4 w-4 mr-2 text-orange-500" />
                            External
                          </div>
                        </td>
                        <td className="px-4 py-3 text-center">
                          <span className="font-semibold text-gray-700">{taskStats.external}</span>
                        </td>
                        <td className="px-4 py-3 text-center">
                          {tasks.filter(t => t.visibility === 'public' && t.status === 'open').length > 0 ? (
                            <span className="font-semibold text-red-600">
                              {tasks.filter(t => t.visibility === 'public' && t.status === 'open').length}
                            </span>
                          ) : (
                            <span className="text-gray-500">0</span>
                          )}
                        </td>
                        <td className="px-4 py-3 text-center">
                          {tasks.filter(t => t.visibility === 'public' && t.status === 'assigned').length > 0 ? (
                            <span className="font-semibold text-blue-600">
                              {tasks.filter(t => t.visibility === 'public' && t.status === 'assigned').length}
                            </span>
                          ) : (
                            <span className="text-gray-500">0</span>
                          )}
                        </td>
                        <td className="px-4 py-3 text-center">
                          {tasks.filter(t => t.visibility === 'public' && t.status === 'in_progress').length > 0 ? (
                            <span className="font-semibold text-yellow-600">
                              {tasks.filter(t => t.visibility === 'public' && t.status === 'in_progress').length}
                            </span>
                          ) : (
                            <span className="text-gray-500">0</span>
                          )}
                        </td>
                        <td className="px-4 py-3 text-center">
                          {tasks.filter(t => t.visibility === 'public' && (t.status === 'closed' || t.status === 'confirmed')).length > 0 ? (
                            <span className="font-semibold text-green-600">
                              {tasks.filter(t => t.visibility === 'public' && (t.status === 'closed' || t.status === 'confirmed')).length}
                            </span>
                          ) : (
                            <span className="text-gray-500">0</span>
                          )}
                        </td>
                        <td className="px-4 py-3 text-center">
                          {tasks.filter(t => t.visibility === 'public' && t.status === 'cancelled').length > 0 ? (
                            <span className="font-semibold text-red-600">
                              {tasks.filter(t => t.visibility === 'public' && t.status === 'cancelled').length}
                            </span>
                          ) : (
                            <span className="text-gray-500">0</span>
                          )}
                        </td>
                        <td className="px-4 py-3 text-center">
                          {tasks.filter(t => t.visibility === 'public' && t.status !== 'closed' && t.status !== 'confirmed' && t.status !== 'cancelled').length > 0 ? (
                            <span className="font-semibold text-orange-600">
                              {tasks.filter(t => t.visibility === 'public' && t.status !== 'closed' && t.status !== 'confirmed' && t.status !== 'cancelled').length}
                            </span>
                          ) : (
                            <span className="text-gray-500">0</span>
                          )}
                        </td>
                      </tr>

                      {/* Total Row */}
                      <tr className="bg-gray-50 border-b border-gray-200 font-medium">
                        <td className="px-4 py-3 font-medium">
                          <div className="flex items-center">
                            <ClipboardList className="h-4 w-4 mr-2 text-blue-500" />
                            Total
                          </div>
                        </td>
                        <td className="px-4 py-3 text-center">
                          <span className="font-semibold text-gray-700">{taskStats.total}</span>
                        </td>
                        <td className="px-4 py-3 text-center">
                          <span className="font-semibold text-red-600">{taskStats.open}</span>
                        </td>
                        <td className="px-4 py-3 text-center">
                          <span className="font-semibold text-blue-600">{taskStats.assigned}</span>
                        </td>
                        <td className="px-4 py-3 text-center">
                          <span className="font-semibold text-yellow-600">{taskStats.inProgress}</span>
                        </td>
                        <td className="px-4 py-3 text-center">
                          <span className="font-semibold text-green-600">{taskStats.completed}</span>
                        </td>
                        <td className="px-4 py-3 text-center">
                          <span className="font-semibold text-red-600">{taskStats.cancelled}</span>
                        </td>
                        <td className="px-4 py-3 text-center">
                          <span className="font-semibold text-orange-600">{taskStats.total - taskStats.completed - taskStats.cancelled}</span>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Tabs for different task views */}
          <Tabs value={activeTab} onValueChange={(value: any) => setActiveTab(value)}>
            <TabsList className="flex flex-wrap w-full justify-start gap-1 h-auto p-1">
              <TabsTrigger value="all" className="flex-shrink-0">All Tasks</TabsTrigger>
              <TabsTrigger value="internal" className="flex-shrink-0 flex items-center gap-2">
                Internal
                {taskStats.internal > 0 && (
                  <Badge variant="secondary" className="text-xs px-1.5 py-0.5 min-w-[1.25rem] h-5">{taskStats.internal}</Badge>
                )}
              </TabsTrigger>
              <TabsTrigger value="external" className="flex-shrink-0 flex items-center gap-2">
                External
                {taskStats.external > 0 && (
                  <Badge variant="secondary" className="text-xs px-1.5 py-0.5 min-w-[1.25rem] h-5">{taskStats.external}</Badge>
                )}
              </TabsTrigger>
              <TabsTrigger value="pending" className="flex-shrink-0 flex items-center gap-2">
                Pending
                {(taskStats.open + taskStats.assigned) > 0 && (
                  <Badge variant="secondary" className="text-xs px-1.5 py-0.5 min-w-[1.25rem] h-5">{taskStats.open + taskStats.assigned}</Badge>
                )}
              </TabsTrigger>
              <TabsTrigger value="in-progress" className="flex-shrink-0 flex items-center gap-2">
                In Progress
                {taskStats.inProgress > 0 && (
                  <Badge variant="secondary" className="text-xs px-1.5 py-0.5 min-w-[1.25rem] h-5">{taskStats.inProgress}</Badge>
                )}
              </TabsTrigger>
              <TabsTrigger value="closed" className="flex-shrink-0 flex items-center gap-2">
                Closed
                {taskStats.completed > 0 && (
                  <Badge variant="secondary" className="text-xs px-1.5 py-0.5 min-w-[1.25rem] h-5">{taskStats.completed}</Badge>
                )}
              </TabsTrigger>
              <TabsTrigger value="cancelled" className="flex-shrink-0 flex items-center gap-2">
                Cancelled
                {taskStats.cancelled > 0 && (
                  <Badge variant="secondary" className="text-xs px-1.5 py-0.5 min-w-[1.25rem] h-5">{taskStats.cancelled}</Badge>
                )}
              </TabsTrigger>
            </TabsList>
          </Tabs>

          {/* Task Table */}
          <Card>
            <CardContent className="p-0">
              <div className="border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Task</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Due Date</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Assignment</TableHead>
                      <TableHead>Budget</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredTasks.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-4">
                          {searchTerm ? (
                            <p>No tasks found matching your search</p>
                          ) : activeTab !== 'all' ? (
                            <p>No tasks found for this filter</p>
                          ) : (
                            <p>No tasks found. Click "New Task" to create your first task.</p>
                          )}
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredTasks.map(task => (
                        <TableRow key={task.id}>
                          <TableCell>
                            <div className="font-medium">{task.title}</div>
                            <div className="text-sm text-gray-500 truncate max-w-[200px]">
                              {task.description}
                            </div>
                          </TableCell>
                          <TableCell>{task.category || 'N/A'}</TableCell>
                          <TableCell>
                            {task.due_date ? new Date(task.due_date).toLocaleDateString() : 'No due date'}
                          </TableCell>
                          <TableCell>{getStatusBadge(task.status)}</TableCell>
                          <TableCell>{getAssignmentBadge(task.visibility)}</TableCell>
                          <TableCell>
                            {task.visibility === 'public' ? (
                              <span className="font-medium">£{task.budget?.toFixed(2) || '0.00'}</span>
                            ) : (
                              <span className="text-gray-500">-</span>
                            )}
                          </TableCell>
                          <TableCell className="text-right">
                            <Button variant="outline" size="sm" asChild>
                              <Link to={`/tasks/${task.id}`}>
                                View
                              </Link>
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-center">
            <Button asChild>
              <Link to="/admin/tasks">
                Go to Task Management
              </Link>
            </Button>
          </div>
        </>
      )}
    </div>
  );
};

export default OrganizationTaskManagement;